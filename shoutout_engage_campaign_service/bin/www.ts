#!/usr/bin/env node

/**
 * Load environment variables first
 */
import * as dotenv from 'dotenv';
import * as path from 'path';
import {fileURLToPath} from 'url';

const wwwFilename = fileURLToPath(import.meta.url);
const wwwDirname = path.dirname(wwwFilename);

// Load environment variables before any other imports
dotenv.config({path: path.resolve(wwwDirname, '../.env')});

/**
 * Module dependencies.
 */

import appPromise from '../app';
import debugLib from 'debug';
import config from '../lib/config';
import {logger} from '../lib/logger';

import {createTerminus} from '@godaddy/terminus';
import RedisConnector from '../lib/db/connectors/RedisConnector';
import MongooseConnector from '../lib/db/connectors/MongooseConnector';
import PrismaConnector from '../lib/db/connectors/PrismaConnector';
import {Worker, isMainThread} from 'worker_threads';

const debug = debugLib('engage:server');
const log = logger(config.logger);

// Track shutdown state
let isShuttingDown = false;
let worker: Worker | null = null;

if (isMainThread) {
    (async () => {
        const {app, server} = await appPromise();

        const port = normalizePort(process.env.PORT || '3002');
        app.set('port', port);

        const healthCheckPath = `${config.api.base_path}/healthcheck`;
        const healthChecks: Record<string, () => Promise<void>> = {
            [healthCheckPath]: onHealthCheck,
        };
        //gracefully shut down on termination signals
        createTerminus(server, {
            signals: ['SIGINT', 'SIGTERM', 'SIGUSR2'],
            healthChecks,
            onSignal,
        });

        server.listen(port);
        server.on('error', onError);
        server.on('listening', onListening);

        async function onSignal(): Promise<void> {
            // Prevent multiple concurrent shutdowns
            if (isShuttingDown) {
                log.info('Shutdown already in progress, ignoring additional signal');
                return;
            }

            isShuttingDown = true;
            log.info('Server is starting cleanup');

            try {
                // Terminate worker thread first
                await shutdownWorker();
            } catch (error:any) {
                log.error(`Error during worker shutdown: ${error instanceof Error ? error.message : String(error || '')}`);
                // Continue with shutdown even if worker shutdown fails
            }

            /**
             * Shutdown worker thread gracefully
             */
            async function shutdownWorker(): Promise<void> {
                if (!worker) {
                    log.info('No worker thread to terminate');
                    return;
                }

                log.info('Terminating worker thread...');

                try {
                    // Don't send multiple shutdown messages
                    worker.postMessage({type: 'shutdown'});

                    // Create a proper shutdown promise with timeout
                    const shutdownPromise = new Promise<void>((resolve) => {
                        // Track if the handler has already been called
                        let handlerCalled = false;

                        const messageHandler = (msg: any) => {
                            // Prevent multiple handler calls
                            if (handlerCalled) return;

                            if (msg.type === 'processors-stopped' || msg.type === 'processors-shutdown-error') {
                                handlerCalled = true;

                                if (msg.type === 'processors-stopped') {
                                    log.info('Job processors stopped successfully');
                                } else {
                                    log.error(`Error stopping job processors: ${String(msg.error)}`);
                                }

                                // Remove this listener
                                worker?.removeListener('message', messageHandler);
                                resolve();
                            }
                        };

                        // Setup a single message handler
                        worker?.once('exit', () => {
                            if (!handlerCalled) {
                                handlerCalled = true;
                                resolve();
                            }
                        });

                        worker?.on('message', messageHandler);

                        // Set timeout for worker shutdown
                        setTimeout(() => {
                            if (!handlerCalled) {
                                handlerCalled = true;
                                worker?.removeListener('message', messageHandler);
                                log.warn('Timeout waiting for job processors to stop');
                                resolve();
                            }
                        }, 5000);
                    });

                    // Wait for processors to respond or timeout
                    await shutdownPromise;

                    // Now terminate the worker
                    if (worker) {
                        await worker.terminate();
                        log.info('Worker thread terminated');
                        worker = null;
                    }
                } catch (error) {
                    log.error(`Error terminating worker: ${error instanceof Error ? error.message : String(error)}`);

                    // Force terminate as last resort
                    if (worker) {
                        try {
                            await worker.terminate();
                            log.info('Worker thread force terminated');
                            worker = null;
                        } catch (terminateError) {
                            log.error(`Failed to force terminate worker: ${String(terminateError)}`);
                        }
                    }
                }
            }

            log.info('cleaning redis cache...');
            log.info('cleaning user cache...');
            await RedisConnector.scanAndDelete('users:*');
            log.info('cleaning modules cache...');
            await RedisConnector.scanAndDelete('groups:*');
            log.info('cleaning groups cache...');
            await RedisConnector.scanAndDelete('modules:*');

            log.info('closing redis connection...');
            await RedisConnector.closeConnection();
            log.info('closing mongo connection...');
            await MongooseConnector.close();
            log.info('closing postgres connection...');
            await PrismaConnector.close();
        }

        async function onHealthCheck(): Promise<void> {
            log.debug(`health check on ${new Date().toISOString()}`);
            // Add actual health checks here if needed.
        }

        function normalizePort(val: string): number | string | false {
            const port = parseInt(val, 10);
            if (isNaN(port)) return val;
            if (port >= 0) return port;
            return false;
        }

        function onError(error: NodeJS.ErrnoException): void {
            if (error.syscall !== 'listen') throw error;

            const bind = typeof port === 'string' ? 'Pipe ' + port : 'Port ' + port;
            switch (error.code) {
                case 'EACCES':
                    console.error(`${bind} requires elevated privileges`);
                    process.exit(1);
                case 'EADDRINUSE':
                    console.error(`${bind} is already in use`);
                    process.exit(1);
                default:
                    throw error;
            }
        }

        function onListening(): void {
            const addr = server.address();
            if (!addr) return;
            const bind =
                typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr.port;
            debug(`Listening on ${bind}`);
            log.info(`server listening on ${bind}`);
        }
    })();

    // Worker is declared at the top level scope now
    try {
        const workerPath = path.join(wwwDirname, '../workers/main.ts');
        worker = new Worker(workerPath);

        // Use a single message handler for all messages
        worker.on('message', (msg) => {
            if (!msg || typeof msg !== 'object' || !msg.type) {
                log.warn(`Received invalid message from worker: ${String(msg)}`);
                return;
            }

            switch (msg.type) {
                case 'error':
                    log.error(`Worker error: ${String(msg.error)}`);
                    if (msg.stack) {
                        log.error(`Worker error stack: ${String(msg.stack)}`);
                    }
                    if (msg.details) {
                        log.error(`Worker error details: ${String(msg.details)}`);
                    }
                    break;
                case 'heartbeat':
                    log.debug(`Worker heartbeat: ${String(msg.timestamp)}`);
                    break;
                case 'processors-stopped':
                    log.info('Job processors stopped successfully');
                    break;
                case 'processors-shutdown-error':
                    log.error(`Error stopping job processors: ${String(msg.error)}`);
                    break;
                default:
                    log.info(`Worker message: ${String(msg)}`);
            }
        });

        worker.on('error', (error: unknown) => {
            log.error(`Worker thread error: ${error instanceof Error ? error.message : String(error)}`);
        });

        worker.on('exit', (code) => {
            if (code !== 0) {
                log.error(`Worker stopped with exit code ${code}`);
            } else {
                log.info('Worker thread exited gracefully');
            }
        });

        log.info('Worker thread started successfully');
    } catch (error: unknown) {
        log.error(`Failed to start worker thread: ${error instanceof Error ? error.message : String(error)}`);
        // Continue without worker if it fails to start
    }
} else {
    log.info('Running on worker thread');
}
