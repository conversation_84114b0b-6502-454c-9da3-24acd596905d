import {parentPort} from 'worker_threads';
import {setWorkerVariables} from './worker.message.handler';

// Simple worker without external dependencies to avoid module loading issues
let isRunning = true;
let heartbeatInterval: NodeJS.Timeout | null = null;


// Worker thread main function
(async function main() {
    try {
        console.log('Worker thread started');

        // Send a message to the main thread
        if (parentPort) {
            parentPort.postMessage({type: 'init', message: 'Worker initialized successfully'});
        }

        // Skip Redis connection test - not required for basic worker functionality
        console.log('Worker initializing without Redis dependency...');

        // Initialize queues first, then processors (with error handling)
        //TODO: Add queue manager and job processors

        // Start heartbeat only if worker is still running
        if (isRunning) {
            heartbeatInterval = setInterval(() => {
                if (isRunning && parentPort) {
                    parentPort.postMessage({type: 'heartbeat', timestamp: new Date().toISOString()});
                }
            }, 60000); // Every 60 seconds
        }

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const errorStack = error instanceof Error ? error.stack : 'No stack trace';

        console.error('Worker initialization error:', errorMessage);
        console.error('Error stack:', errorStack);

        if (parentPort) {
            parentPort.postMessage({
                type: 'error',
                error: errorMessage,
                stack: errorStack,
                details: error
            });
        }
        process.exit(1);
    }
}());

/**
 * Stop all job processors gracefully
 */
async function stopJobProcessors() {
    try {
        console.log('Stopping job processors...');

        const shutdownPromises: Promise<void>[] = [];

        // Wait for all processors to stop
        await Promise.all(shutdownPromises);


        console.log('All job processors and queues stopped successfully');

        // Send success message to main thread
        if (parentPort) {
            parentPort.postMessage({
                type: 'processors-stopped',
                message: 'Job processors and queues stopped successfully'
            });
        }

    } catch (error) {
        console.error('Error stopping job processors:', error);

        // Send error message to main thread
        if (parentPort) {
            parentPort.postMessage({
                type: 'processors-shutdown-error',
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }
}

// Set worker variables in the message handler
setWorkerVariables(isRunning, heartbeatInterval, stopJobProcessors);