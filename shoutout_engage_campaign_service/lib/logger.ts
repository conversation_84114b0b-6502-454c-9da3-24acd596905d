/**
 * Logger module for the application
 * 
 * This module provides a centralized logging system using Pino.
 * It includes special handling for test environments to prevent open handles in Jest.
 * 
 * IMPORTANT: When using this logger in tests, make sure to call closeLoggers() in the
 * afterAll hook to properly clean up resources and prevent open handles.
 * 
 * Example in tests/setup.ts:
 * ```
 * afterAll(async () => {
 *   // Other cleanup...
 *   await closeLoggers();
 * });
 * ```
 */
import pino, { Logger } from 'pino';

type LoggerConfig = {
    name?: string;
    level?: string;
};

/**
 * Interface for tracking both logger and transport instances together
 * This allows us to properly close both when needed
 */
interface LoggerWithTransport {
    logger: Logger;
    transport?: any; // Transport type is not well-defined in Pino types
}

/**
 * Manager class for tracking and closing logger instances
 * 
 * This class keeps track of all logger instances created and provides
 * methods to close them properly, preventing open handles in Jest tests.
 */
class LoggerManager {
    // Properly initialize static properties
    private static instances: LoggerWithTransport[] = [];
    private static isTestEnvironment: boolean = process.env.NODE_ENV === 'test';

    /**
     * Add a logger instance (and optional transport) to the manager
     */
    static addInstance(logger: Logger, transport?: any): void {
        if (!this.instances) {
            this.instances = [];
        }
        this.instances.push({ logger, transport });
    }

    /**
     * Close all logger and transport instances
     * 
     * This method properly flushes all loggers and closes all transports,
     * preventing open handles in Jest tests.
     * 
     * @returns Promise that resolves when all loggers and transports are closed
     */
    static closeAll(): Promise<void> {
        // Handle case where no instances exist yet
        if (!this.instances || this.instances.length === 0) {
            return Promise.resolve();
        }

        const closePromises = this.instances.map(({ logger, transport }) => {
            return new Promise<void>((resolve) => {
                // First flush the logger
                if (logger && typeof logger.flush === 'function') {
                    logger.flush(() => {
                        // Then close the transport if it exists
                        if (transport && typeof transport.end === 'function') {
                            transport.end();
                        }
                        resolve();
                    });
                } else if (transport && typeof transport.end === 'function') {
                    // If logger doesn't have flush, just close the transport
                    transport.end();
                    resolve();
                } else {
                    // If neither can be closed, resolve immediately
                    resolve();
                }
            });
        });

        return Promise.all(closePromises).then(() => {
            // Clear the instances array after closing all loggers
            this.instances = [];
        });
    }

    /**
     * Check if we're in a test environment
     */
    static isTest(): boolean {
        return this.isTestEnvironment;
    }
}

/**
 * Create a logger instance with the specified configuration
 * 
 * In test environments, this creates a simpler logger without transports
 * to avoid creating worker threads that might cause open handles in Jest.
 * 
 * In non-test environments, it creates a full-featured logger with pretty printing.
 * 
 * @param config Logger configuration
 * @returns Configured Pino logger instance
 */
export const logger = (config: LoggerConfig): Logger => {
    // In test environment, use a simpler logger configuration to avoid worker threads
    if (LoggerManager.isTest()) {
        const instance = pino({
            name: config.name || 'AppLogger',
            level: config.level || 'info',
            // No transport in test environment to avoid worker threads
        });
        
        LoggerManager.addInstance(instance);
        return instance;
    }

    // In non-test environment, use the full configuration with transport
    const transport = pino.transport({
        target: 'pino-pretty',
        options: {
            colorize: true,
            translateTime: 'yyyy-mm-dd HH:MM:ss.l',
            ignore: 'pid,hostname',
        }
    });

    const instance = pino(
        {
            name: config.name || 'AppLogger',
            level: config.level || 'info',
        },
        transport
    );

    // Add the instance to the manager
    LoggerManager.addInstance(instance, transport);
    
    return instance;
};

/**
 * Close all logger instances and their transports
 * 
 * This function should be called in the afterAll hook of Jest tests
 * to prevent open handles from keeping Jest from exiting.
 * 
 * @returns Promise that resolves when all loggers and transports are closed
 */
export const closeLoggers = (): Promise<void> => {
    return LoggerManager.closeAll();
};
