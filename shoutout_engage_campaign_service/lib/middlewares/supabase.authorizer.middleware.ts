
import { Request, Response, NextFunction } from 'express';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import envConfig from '../../config';
import yamlConfig from '../config';
import {ProfileDAO} from '../db/dao/prisma/ProfileDAO';
import { logger } from '../logger';

const log = logger(yamlConfig.logger);

export interface AuthenticatedRequest extends Request {
    user?: any;
    userId?: string;
    organizationId?: string;
}

/**
 * SupabaseAuthMiddleware class for handling authentication and authorization
 * using Supabase authentication service
 */
class SupabaseAuthMiddleware {
    private static instance: SupabaseAuthMiddleware;
    private supabase: SupabaseClient | null = null;

    /**
     * Private constructor to enforce singleton pattern
     */
    private constructor() {}

    /**
     * Get the singleton instance of SupabaseAuthMiddleware
     */
    public static getInstance(): SupabaseAuthMiddleware {
        if (!SupabaseAuthMiddleware.instance) {
            SupabaseAuthMiddleware.instance = new SupabaseAuthMiddleware();
        }
        return SupabaseAuthMiddleware.instance;
    }

    /**
     * Get or initialize the Supabase client
     */
    public getClient(): SupabaseClient {
        if (!this.supabase) {
            this.supabase = createClient(
                envConfig.SUPABASE_URL!,
                envConfig.SUPABASE_SERVICE_ROLE_KEY!
            );
        }
        return this.supabase;
    }

    /**
     * Set a custom Supabase client (useful for testing)
     */
    public setClient(client: SupabaseClient): void {
        this.supabase = client;
    }

    /**
     * Extract token from authorization header
     */
    private extractToken(authHeader: string): string | null {
        if (authHeader === 'Bearer') {
            return null;
        }

        const token = authHeader.startsWith('Bearer ')
            ? authHeader.slice(7).trim()
            : authHeader.trim();

        return token && token !== '' ? token : null;
    }

    /**
     * Middleware to authenticate requests using Supabase
     */
    public async authenticate(
        req: AuthenticatedRequest,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            // Get the authorization header
            const authHeader = req.headers.authorization;

            if (!authHeader) {
                res.status(401).json({
                    error: 'Authorization header is required',
                    errorCode: 'AUTH_001'
                });
                return;
            }

            const token = this.extractToken(authHeader);

            if (!token) {
                res.status(401).json({
                    error: 'Authentication token is required',
                    errorCode: 'AUTH_002'
                });
                return;
            }

            // Verify the token with Supabase
            const { data, error } = await this.getClient().auth.getUser(token);

            if (error || !data.user) {
                log.warn('Authentication failed:', error?.message || 'User not found');
                res.status(401).json({
                    error: 'Invalid or expired authentication token',
                    errorCode: 'AUTH_003'
                });
                return;
            }

            // Add user information to request object
            req.user = data.user;
            req.userId = data.user.id;

            // Get user's organization from their profile
            const profile = await ProfileDAO.getProfileByUserId(data.user.id);

            if (profile?.organization_uuid) {
                req.organizationId = profile.organization_uuid;
                log.info('User authenticated successfully with organization:', { 
                    userId: data.user.id, 
                    email: data.user.email,
                    organizationId: profile.organization_uuid 
                });
            } else {
                log.info('User authenticated successfully (no organization):', { 
                    userId: data.user.id, 
                    email: data.user.email 
                });
            }

            next();
        } catch (error) {
            log.error('Authentication error:', error);
            res.status(500).json({
                error: 'Internal server error during authentication',
                errorCode: 'AUTH_004'
            });
        }
    }

    /**
     * Middleware factory to check if user has specific roles
     */
    public requireRole(roles: string[]) {
        return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
            try {
                if (!req.user) {
                    res.status(401).json({
                        error: 'User not authenticated',
                        errorCode: 'AUTH_005'
                    });
                    return;
                }

                // Get user metadata/roles from Supabase
                const { data: userProfile, error } = await this.getClient()
                    .from('user_profiles') // Assuming you have a user_profiles table
                    .select('role')
                    .eq('user_id', req.user.id)
                    .single();

                if (error) {
                    log.warn('Failed to get user role:', error.message);
                    res.status(403).json({
                        error: 'Unable to verify user permissions',
                        errorCode: 'AUTH_006'
                    });
                    return;
                }

                const userRole = userProfile?.role || 'user';

                if (!roles.includes(userRole)) {
                    res.status(403).json({
                        error: 'Insufficient permissions',
                        errorCode: 'AUTH_007'
                    });
                    return;
                }

                next();
            } catch (error) {
                log.error('Role check error:', error);
                res.status(500).json({
                    error: 'Internal server error during role verification',
                    errorCode: 'AUTH_008'
                });
            }
        };
    }

    /**
     * Middleware for admin-only routes
     */
    public requireAdmin() {
        return this.requireRole(['admin', 'super_admin']);
    }

    /**
     * Middleware for portal access
     */
    public requirePortalAccess() {
        return this.requireRole(['admin', 'super_admin', 'portal_user']);
    }

    /**
     * Optional authentication middleware (doesn't fail if no token)
     */
    public async optionalAuth(
        req: AuthenticatedRequest,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const authHeader = req.headers.authorization;

            if (!authHeader) {
                next();
                return;
            }

            if (authHeader === 'Bearer') {
                next();
                return;
            }

            const token = this.extractToken(authHeader);

            if (token) {
                const { data, error } = await this.getClient().auth.getUser(token);

                if (!error && data.user) {
                    req.user = data.user;
                    req.userId = data.user.id;
                }
            }

            next();
        } catch (error) {
            log.warn('Optional auth error:', error);
            next(); // Continue without authentication
        }
    }

    /**
     * Static method for authentication middleware (for backward compatibility)
     */
    public static async authenticate(
        req: AuthenticatedRequest,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        return SupabaseAuthMiddleware.getInstance().authenticate(req, res, next);
    }

    /**
     * Static method for optional authentication middleware (for backward compatibility)
     */
    public static async optionalAuth(
        req: AuthenticatedRequest,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        return SupabaseAuthMiddleware.getInstance().optionalAuth(req, res, next);
    }

    /**
     * Static method for role-based middleware factory (for backward compatibility)
     */
    public static requireRole(roles: string[]) {
        return SupabaseAuthMiddleware.getInstance().requireRole(roles);
    }

    /**
     * Static method for admin-only middleware (for backward compatibility)
     */
    public static requireAdmin() {
        return SupabaseAuthMiddleware.getInstance().requireAdmin();
    }

    /**
     * Static method for portal access middleware (for backward compatibility)
     */
    public static requirePortalAccess() {
        return SupabaseAuthMiddleware.getInstance().requirePortalAccess();
    }
}

// Export the class
export default SupabaseAuthMiddleware;

// For backward compatibility, export the same functions that were previously used
export const getSupabaseClient = (): SupabaseClient => {
    return SupabaseAuthMiddleware.getInstance().getClient();
};

export const supabaseAuthMiddleware = SupabaseAuthMiddleware.authenticate;
export const optionalAuth = SupabaseAuthMiddleware.optionalAuth;
export const requireRole = SupabaseAuthMiddleware.requireRole;
export const requireAdmin = SupabaseAuthMiddleware.requireAdmin();
export const requirePortalAccess = SupabaseAuthMiddleware.requirePortalAccess();
