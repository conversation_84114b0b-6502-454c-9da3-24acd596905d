import { PrismaClient } from '../../../generated/prisma';
import { logger } from '../../logger';
import config from '../../config';

const log = logger(config.logger);

class PrismaConnector {
    private static prisma: PrismaClient;

    /**
     * Initialize Prisma Client if not already initialized
     */
    static initialize(): PrismaClient {
        if (!this.prisma) {
            this.prisma = new PrismaClient();
            log.info('🟢 Prisma Client initialized');
        }

        return this.prisma;
    }

    /**
     * Get the Prisma Client instance (must call `initialize()` first)
     */
    static getClient(): PrismaClient {
        if (!this.prisma) {
            throw new Error('❌ Prisma client not initialized. Call `PrismaConnector.initialize()` first.');
        }
        return this.prisma;
    }

    /**
     * Disconnect Prisma Client
     */
    static async close(): Promise<void> {
        if (this.prisma) {
            await this.prisma.$disconnect();
            log.info('🔴 Prisma Client disconnected');
        }
    }
}

export default PrismaConnector;
