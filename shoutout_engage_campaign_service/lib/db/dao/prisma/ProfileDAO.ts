import PrismaConnector from "../../connectors/PrismaConnector";
import { UserProfile } from "@shoutout/engage-types";
import { logger } from '../../../logger';
import config from '../../../config';
import { PrismaClientKnownRequestError,PrismaClientValidationError } from "../../../../generated/prisma/runtime/library";

const log = logger(config.logger);

export class ProfileDAO {
    /**
     * Get profile by user ID
     * @param userId - User ID
     * @returns Promise<UserProfile | null | undefined> - The user profile, null if not found, or undefined in some edge cases
     */
    static async getProfileByUserId(userId: string): Promise<UserProfile | null | undefined> {
        try {
            const prisma = PrismaConnector.getClient();
            const profile = await prisma.profiles.findUnique({
                where: {
                    user_id: userId
                }
            });
            if (!profile) {
                return null;
            }
            return {
                uuid: profile.uuid,
                user_id: profile.user_id,
                organization_uuid: profile.organization_uuid,
                first_name: profile.first_name,
                last_name: profile.last_name,
                user_type: profile.user_type,
                email: profile.email,
                phone_number: profile.phone_number,
                is_email_verified: profile.is_email_verified,
                is_mobile_verified: profile.is_mobile_verified,
                user_status: profile.user_status,
                created_at: profile.created_at,
                updated_at: profile.updated_at
            };


        } catch (error) {
            // Handle Prisma unique constraint violation (equivalent to MongoDB duplicate key error)
            if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
                const duplicateField = this.extractDuplicateField(error);
                log.error(`Unique constraint violation for field: ${duplicateField}`, error);
                throw new Error(`Profile with ${duplicateField} already exists`);
            }

            // Handle validation errors
            if (error instanceof PrismaClientValidationError) {
                log.error('Profile validation error:', error.message);
                throw new Error(`Validation failed: ${error.message}`);
            }

            // Handle other database errors
            log.error('Error getting profile by user ID:', error);
            throw new Error('Failed to retrieve profile due to database error');
        }
    }

    /**
     * Extract the duplicate field name from Prisma unique constraint violation error
     * @param error - The Prisma error object
     * @returns string - The field name that caused the unique constraint violation
     */
    private static extractDuplicateField(error: PrismaClientKnownRequestError): string {
        // Prisma P2002 errors include a meta property with target array containing the field names
        if (error.meta?.target && Array.isArray(error.meta.target)) {
            return error.meta.target[0] || 'unknown field';
        }

        // Fallback
        return 'unknown field';
    }
}
