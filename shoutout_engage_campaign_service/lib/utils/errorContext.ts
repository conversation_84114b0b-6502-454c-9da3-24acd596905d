/**
 * Error Context Utility (shared implementation)
 * Provides context information for enhanced error logging and debugging
 */

import { Request } from 'express';
import type { ErrorContextData } from '@shoutout/engage-types';

/**
 * Class for capturing and managing error context information
 */
export class ErrorContext {
  public requestId: string;
  public userId?: string;
  public organizationId?: string;
  public endpoint: string;
  public method: string;
  public userAgent?: string;
  public ip?: string;
  public timestamp: Date;
  public jobId?: string;
  public fileName?: string;
  public rowNumber?: number;
  [key: string]: any;

  /**
   * Create a new ErrorContext from an Express request
   * @param req - Express request object
   * @param additionalContext - Optional additional context information
   */
  constructor(req: Request, additionalContext?: Record<string, any>) {
    // Extract basic request information
    this.requestId = (req as any).requestId || `req-${Date.now()}-${Math.random().toString(36).slice(2, 11)}`;
    this.endpoint = req.originalUrl || req.url || 'unknown';
    this.method = req.method || 'unknown';
    this.userAgent = req.get ? req.get('User-Agent') : undefined;
    this.ip = this.extractClientIP(req);
    this.timestamp = new Date();

    // Extract user information if available
    if ((req as any).userId) {
      this.userId = (req as any).userId;
    }

    if ((req as any).organizationId) {
      this.organizationId = (req as any).organizationId;
    }

    // Add additional context if provided
    if (additionalContext) {
      Object.keys(additionalContext).forEach(key => {
        // Handle rowNumber=0 case specially
        if (key === 'rowNumber' && additionalContext[key] === 0) {
          this[key] = 0;
        } else if (additionalContext[key] !== undefined) {
          this[key] = additionalContext[key];
        }
      });
    }
  }

  /**
   * Extract client IP address from request
   * @param req - Express request object
   * @returns Client IP address
   */
  private extractClientIP(req: Request): string {
    // Only try headers if the header method exists
    if (req.header) {
      // Try X-Forwarded-For header
      const forwardedFor = req.header('x-forwarded-for') || req.header('X-Forwarded-For');
      if (forwardedFor) {
        // Get the first IP in the list (client IP)
        return forwardedFor.split(',')[0].trim();
      }

      // Try X-Real-IP header
      const realIP = req.header('x-real-ip') || req.header('X-Real-IP');
      if (realIP) {
        return realIP;
      }
    }

    // Prefer express' req.ip when trust proxy is configured
    if (req.ip) {
      return req.ip;
    }

    // Fallback to connection.remoteAddress (deprecated but still used)
    if ((req as any).connection && (req as any).connection.remoteAddress) {
      return (req as any).connection.remoteAddress;
    }

    // Fallback to socket.remoteAddress (replacement for deprecated req.connection.remoteAddress)
    if (req.socket && req.socket.remoteAddress) {
      return req.socket.remoteAddress;
    }

    // Default if no IP can be determined
    return 'unknown';
  }

  /**
   * Create a new ErrorContext with additional context information
   * @param additionalContext - Additional context to add
   * @returns New ErrorContext instance with merged context
   */
  public withAdditionalContext(additionalContext: Record<string, any>): ErrorContext {
    // Create a new instance
    const newContext = new ErrorContext({} as Request);

    // Copy all properties from this context
    Object.keys(this).forEach(key => {
      newContext[key] = this[key];
    });

    // Add additional context
    Object.keys(additionalContext).forEach(key => {
      // Handle rowNumber=0 case specially
      if (key === 'rowNumber' && additionalContext[key] === 0) {
        newContext[key] = 0;
      } else if (additionalContext[key] !== undefined) {
        newContext[key] = additionalContext[key];
      }
    });

    return newContext;
  }

  /**
   * Convert context to a plain object for logging
   * @returns Plain object representation of the context
   */
  public toLogObject(): Record<string, any> {
    const logObject: Record<string, any> = {
      requestId: this.requestId,
      endpoint: this.endpoint,
      method: this.method,
      timestamp: this.timestamp.toISOString(),
    };

    // Add optional fields if they exist
    if (this.ip) logObject.ip = this.ip;
    if (this.userAgent) logObject.userAgent = this.userAgent;
    if (this.userId) logObject.userId = this.userId;
    if (this.organizationId) logObject.organizationId = this.organizationId;
    if (this.jobId) logObject.jobId = this.jobId;
    if (this.fileName) logObject.fileName = this.fileName;

    // Handle rowNumber=0 case specially
    if (this.rowNumber === 0 || this.rowNumber) {
      logObject.rowNumber = this.rowNumber;
    }

    // Add any other custom properties
    Object.keys(this).forEach(key => {
      if (!logObject[key] &&
          !['requestId', 'endpoint', 'method', 'timestamp', 'ip', 'userAgent',
            'userId', 'organizationId', 'jobId', 'fileName', 'rowNumber'].includes(key) &&
          this[key] !== undefined) {
        logObject[key] = this[key];
      }
    });

    return logObject;
  }

  /**
   * Create an ErrorContext from a plain object
   * @param contextData - Object containing context data
   * @returns New ErrorContext instance
   */
  public static fromObject(contextData: ErrorContextData): ErrorContext {
    const context = new ErrorContext({} as Request);

    // Copy all properties from the context data
    Object.keys(contextData).forEach(key => {
      (context as any)[key] = (contextData as any)[key];
    });

    return context;
  }
}
