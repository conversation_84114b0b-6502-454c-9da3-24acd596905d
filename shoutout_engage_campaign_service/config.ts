import * as dotenv from 'dotenv';
import * as path from 'path';

// Use a simple path resolution that works in both runtime and test environments
const configDirname = process.cwd();

// Load environment variables
dotenv.config({ path: path.resolve(configDirname, '.env') });

interface AppConfig {
    NODE_ENV: string;
    PORT: string;
    SUPABASE_URL: string;
    SUPABASE_SERVICE_ROLE_KEY: string;
    MONGO_URL: string;
    SWAGGER_SERVER_URL: string;
    PRISMA_DATABASE_URL: string;
    PRISMA_DIRECT_URL: string;
}

function getEnvVar(name: keyof AppConfig): string {
    const value = process.env[name];
    if (!value) {
        throw new Error('Missing required environment variable: ' + name);
    }
    return value;
}

const config: AppConfig = {
    NODE_ENV: getEnvVar('NODE_ENV'),
    PORT: getEnvVar('PORT'),
    SUPABASE_URL: getEnvVar('SUPABASE_URL'),
    SUPABASE_SERVICE_ROLE_KEY: getEnvVar('SUPABASE_SERVICE_ROLE_KEY'),
    MONGO_URL: getEnvVar('MONGO_URL'),
    SWAGGER_SERVER_URL: getEnvVar('SWAGGER_SERVER_URL'),
    PRISMA_DATABASE_URL: getEnvVar('PRISMA_DATABASE_URL'),
    PRISMA_DIRECT_URL: getEnvVar('PRISMA_DIRECT_URL'),
};

export default config;
