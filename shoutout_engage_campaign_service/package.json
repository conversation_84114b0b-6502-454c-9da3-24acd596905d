{"name": "shoutout-engage-campaign-service", "version": "1.0.0", "description": "Backend service for Shoutute Engage Campaigns Service", "main": "bin/www", "type": "module", "private": true, "scripts": {"build": "tsc", "start": "PORT=3002 node ./bin/www.js", "dev:www-campaign": "PORT=3002 nodemon --exec 'node --import ./register.mjs --no-deprecation' ./bin/www.ts", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "check": "npm run type-check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:ci": "jest --coverage --watchAll=false --passWithNoTests"}, "dependencies": {"@godaddy/terminus": "^4.12.1", "@prisma/client": "^6.12.0", "@shoutout/engage-types": "file:../shoutout_engage_types", "@shoutout/engage-utils": "file:../shoutout_engage_utils", "@supabase/supabase-js": "^2.0.0", "@types/bull": "^4.10.4", "@types/cookie-parser": "^1.4.9", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "bull": "^4.16.5", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.18.0", "helmet": "^8.1.0", "joi": "^17.13.3", "mongoose": "^8.16.3", "node-yaml-config": "^1.0.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "redis": "^5.6.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/debug": "^4.1.12", "@types/express": "^4.17.17", "@types/jest": "^30.0.0", "@types/joi": "^17.2.3", "@types/node": "^18.15.11", "@types/pino": "^7.0.4", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "jest": "^30.0.4", "jest-environment-node": "^30.0.4", "mongodb-memory-server": "^10.1.4", "nodemon": "^2.0.0", "prisma": "^6.12.0", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "engines": {"node": ">=14.0.0"}}