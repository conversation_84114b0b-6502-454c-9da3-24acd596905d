import type { ErrorResponse, ErrorContextData } from '@shoutout/engage-types';

/**
 * Base application error class that extends the built-in Error class
 * Provides additional properties for HTTP status code, error code, and operational status
 */
export class AppError extends Error {
  /**
   * HTTP status code for the error
   */
  public statusCode: number;

  /**
   * Error code for identifying the error type
   */
  public errorCode: string;

  /**
   * Whether the error is operational (expected) or programming (unexpected)
   * Operational errors are expected errors that should be handled gracefully
   * Programming errors are unexpected errors that indicate a bug in the code
   */
  public isOperational: boolean;

  /**
   * Optional details for field-specific errors
   */
  public details?: Array<{
    field: string;
    message: string;
  }>;

  /**
   * Optional context information for enhanced error logging and debugging
   * Contains request context, user information, and additional metadata
   */
  public context?: Partial<ErrorContextData>;

  /**
   * Create a new AppError
   * @param message - Error message
   * @param statusCode - HTTP status code
   * @param errorCode - Error code for identifying the error type
   * @param isOperational - Whether the error is operational (expected) or programming (unexpected)
   * @param details - Optional details for field-specific errors
   * @param context - Optional context information for enhanced error logging
   */
  constructor(
    message: string,
    statusCode: number = 500,
    errorCode: string = 'INTERNAL_001',
    isOperational: boolean = true,
    details?: Array<{ field: string; message: string }>,
    context?: Partial<ErrorContextData>
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.isOperational = isOperational;
    this.details = details;
    this.context = context;

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Add or merge context information to the error
   * This method creates a new instance with the merged context while preserving the error type
   * @param context - Context information to add or merge
   * @returns New error instance with merged context
   */
  public withContext(context: Partial<ErrorContextData>): this {
    // Merge existing context with new context
    const mergedContext = {
      ...this.context,
      ...context
    };

    // Create a new instance by copying the current error
    const errorWithContext = Object.create(Object.getPrototypeOf(this)) as this;
    
    // Manually copy all properties to ensure they're preserved
    errorWithContext.message = this.message;
    errorWithContext.name = this.name;
    errorWithContext.statusCode = this.statusCode;
    errorWithContext.errorCode = this.errorCode;
    errorWithContext.isOperational = this.isOperational;
    errorWithContext.details = this.details;
    errorWithContext.context = mergedContext;
    errorWithContext.stack = this.stack;

    return errorWithContext;
  }

  /**
   * Get the context information associated with this error
   * @returns Context information or undefined if no context is set
   */
  public getContext(): Partial<ErrorContextData> | undefined {
    return this.context;
  }

  /**
   * Check if the error has context information
   * @returns True if context is available, false otherwise
   */
  public hasContext(): boolean {
    return this.context !== undefined && Object.keys(this.context).length > 0;
  }

  /**
   * Convert the error to an ErrorResponse object
   * @returns ErrorResponse object
   */
  toErrorResponse(): ErrorResponse {
    const response: ErrorResponse = {
      error: this.message,
      errorCode: this.errorCode
    };

    if (this.details) {
      response.details = this.details;
    }

    return response;
  }
}