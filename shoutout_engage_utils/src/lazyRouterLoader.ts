import type { RequestHandler, Request, Response, NextFunction } from 'express';

/**
 * Lazily loads an Express router/module when the first request hits it.
 *
 * Usage in service app.ts:
 *   const contactsRoute = lazyLoadRoute(() => import('./routes/contacts'));
 *   app.use('/api/contacts', contactsRoute);
 *
 * The imported module can export either:
 *   - default export: an Express Router/handler
 *   - named export `router`: an Express Router/handler
 *   - the module itself being a function compatible with (req,res,next)
 */
export function lazyLoadRoute<TModule>(loader: () => Promise<TModule>): RequestHandler {
  let cachedHandler: any | null = null;
  let loadingPromise: Promise<any> | null = null;

  const ensureLoaded = async (): Promise<any> => {
    if (cachedHandler) return cachedHandler;
    if (!loadingPromise) {
      loadingPromise = loader()
        .then((mod: any) => {
          // Try default export, then `router`, then module itself
          const handler = mod?.default ?? mod?.router ?? mod;
          cachedHandler = handler;
          return handler;
        })
        .finally(() => {
          // Avoid holding stale promise
          loadingPromise = null;
        });
    }
    return loadingPromise;
  };

  const handler: RequestHandler = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const loaded = await ensureLoaded();
      return typeof loaded === 'function' ? loaded(req, res, next) : next();
    } catch (err) {
      return next(err);
    }
  };

  return handler;
}

export default lazyLoadRoute;
