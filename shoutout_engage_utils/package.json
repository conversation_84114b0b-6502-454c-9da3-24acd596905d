{"name": "@shoutout/engage-utils", "version": "1.0.0", "description": "Shared utilities for ShoutOUT Engage services", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "keywords": ["shoutout", "engage", "utilities", "shared"], "author": "ShoutOUT Labs", "license": "MIT", "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "files": ["dist/**/*"], "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./pathUtils": {"import": "./dist/pathUtils.js", "require": "./dist/pathUtils.js", "types": "./dist/pathUtils.d.ts"}, "./lazyRouterLoader": {"import": "./dist/lazyRouterLoader.js", "require": "./dist/lazyRouterLoader.js", "types": "./dist/lazyRouterLoader.d.ts"}}, "dependencies": {"express": "^5.1.0"}}