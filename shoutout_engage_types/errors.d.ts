/**
 * Error related shared types
 */

// Subset of HTTP status codes we commonly use
export type HttpStatusCode = 400 | 401 | 403 | 404 | 409 | 422 | 500;

// Common error code pattern: [DOMAIN]_[NNN]
export type ErrorCode =
  | 'VALIDATION_001'
  | 'BAD_REQUEST_001'
  | 'DUPLICATE_001'
  | 'NOT_FOUND_001'
  | 'UNAUTHORIZED_001'
  | 'FORBIDDEN_001'
  | 'DB_001'
  | 'INTERNAL_001'
  | (string & {}); // allow other domain-specific codes

// Structural type describing our AppError shape (runtime classes stay in services)
export interface AppErrorLike extends Error {
  statusCode?: HttpStatusCode | number;
  errorCode?: ErrorCode | string;
  isOperational?: boolean;
  details?: Array<{ field: string; message: string }>;
  // Optional context container (kept loose to avoid cycles)
  context?: Partial<import('./context').ErrorContextData> | Record<string, any>;
}

// Express error handler function signature exported for convenience
export type ErrorRequestHandlerLike = (
  err: unknown,
  req: import('express').Request,
  res: import('express').Response,
  next: import('express').NextFunction
) => unknown;
