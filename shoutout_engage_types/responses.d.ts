/**
 * API response related types
 */

// Fine-grained error detail used across services
export interface ErrorDetail {
  field: string;
  message: string;
}

// Standard error response interface
export interface ErrorResponse {
  error: string;
  errorCode?: string;
  details?: Array<ErrorDetail>;
}

// Standard success response wrapper
export interface SuccessResponse<T> {
  data: T;
  message?: string;
}

// Pagination metadata
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Paginated response wrapper
export interface PaginatedResponse<T> {
  data: T[];
  meta: PaginationMeta;
}
