/**
 * Organization related shared types
 */

// Branded type alias for organization identifier (kept simple for broad compatibility)
export type OrganizationId = string;

// Lightweight organization shape shared across services
export interface Organization {
  id: OrganizationId;
  name: string;
  status?: 'active' | 'inactive';
  createdAt?: Date;
  updatedAt?: Date;
  // Allow additional fields without forcing tight coupling to one service's DB model
  [key: string]: any;
}

// Common context shape that appears in requests and logs
export interface OrganizationContext {
  organizationId: OrganizationId;
  organizationName?: string;
}
