/**
 * Error context and middleware related shared types
 */

// Common request shape used by error-context middleware
export interface RequestWithErrorContext extends import('express').Request {
  requestId: string;
  errorContext?: Record<string, any>;
  user?: {
    id: string;
    organizationId?: string;
    [key: string]: any;
  };
}

// Error context data (shared)
export interface ErrorContextData {
  requestId: string;
  userId?: string;
  organizationId?: string;
  endpoint: string;
  method: string;
  userAgent?: string;
  ip?: string;
  timestamp: Date;
  jobId?: string;
  fileName?: string;
  rowNumber?: number;
  [key: string]: any;
}
